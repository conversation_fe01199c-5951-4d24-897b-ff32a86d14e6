// components/OnboardingFlow.tsx

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Bar<PERSON>hart3, Brain, CheckCircle, Heart, Languages } from "lucide-react-native";
import React, { useState } from "react";
import { Pressable, Text, View } from "react-native";

interface OnboardingFlowProps {
  onComplete: (selectedMode: string) => void;
  language: string;
  toggleLanguage: () => void;
  t: (key: string) => string;
}

const OnboardingFlow: React.FC<OnboardingFlowProps> = ({
  onComplete,
  language,
  toggleLanguage,
  t,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedMode, setSelectedMode] = useState("");

  const modes = [
    {
      id: 'planning',
      icon: '🤰',
      title: t('onboarding.modes.planning.title'),
      description: t('onboarding.modes.planning.description'),
      color: 'bg-primary/10 text-primary border-primary/20'
    },
    {
      id: 'pregnancy',
      icon: '🤱',
      title: t('onboarding.modes.pregnancy.title'),
      description: t('onboarding.modes.pregnancy.description'),
      color: 'bg-accent/30 text-accent-foreground border-accent/40'
    },
    {
      id: 'newborn',
      icon: '👶',
      title: t('onboarding.modes.newborn.title'),
      description: t('onboarding.modes.newborn.description'),
      color: 'bg-green-100 text-green-700 border-green-200'
    }
  ];

  const features = [
    {
      icon: Brain,
      title: t('onboarding.features.ai.title'),
      description: t('onboarding.features.ai.description')
    },
    {
      icon: Heart,
      title: t('onboarding.features.tracking.title'),
      description: t('onboarding.features.tracking.description')
    },
    {
      icon: BarChart3,
      title: t('onboarding.features.insights.title'),
      description: t('onboarding.features.insights.description')
    }
  ];

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(selectedMode);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <View className="items-center space-y-6">
            <View className="space-y-2">
              <Text className="text-3xl font-bold text-foreground text-center">
                {t("onboarding.welcome.title")}
              </Text>
              <Text className="text-xl text-primary text-center">
                {t("onboarding.welcome.subtitle")}
              </Text>
              <Text className="text-muted-foreground text-center max-w-xs">
                {t("onboarding.welcome.description")}
              </Text>
            </View>
            <View className="w-32 h-32 rounded-full bg-pink-100 items-center justify-center">
              <Text className="text-6xl">🤱</Text>
            </View>
          </View>
        );
  
      case 1:
        return (
          <View className="space-y-6">
            <View className="space-y-2 items-center">
              <Text className="text-2xl font-bold text-foreground">
                {t("onboarding.modes.title")}
              </Text>
              <Text className="text-muted-foreground">
                {t("onboarding.modes.subtitle")}
              </Text>
            </View>
            <View className="space-y-3">
              {modes.map((mode) => (
                <Pressable
                  key={mode.id}
                  onPress={() => setSelectedMode(mode.id)}
                  className={cn(
                    "w-full flex-row items-start space-x-3 rounded-lg p-4 border",
                    selectedMode === mode.id
                      ? "bg-primary border-primary"
                      : "bg-background/50 border-border"
                  )}
                >
                  <Text className="text-2xl">{mode.icon}</Text>
                  <View className="flex-1">
                    <Text
                      className={cn(
                        "font-medium",
                        selectedMode === mode.id
                          ? "text-primary-foreground"
                          : "text-foreground"
                      )}
                    >
                      {mode.title}
                    </Text>
                    <Text
                      className={cn(
                        "text-sm",
                        selectedMode === mode.id
                          ? "text-primary-foreground/80"
                          : "text-muted-foreground"
                      )}
                    >
                      {mode.description}
                    </Text>
                  </View>
                  {selectedMode === mode.id && (
                    <CheckCircle size={20} className="text-primary-foreground" />
                  )}
                </Pressable>
              ))}
            </View>
          </View>
        );
  
      case 2:
        return (
          <View className="space-y-6">
            <View className="space-y-2 items-center">
              <Text className="text-2xl font-bold text-foreground">
                {t("onboarding.features.title")}
              </Text>
              <Text className="text-muted-foreground">
                {t("onboarding.features.subtitle")}
              </Text>
            </View>
            <View className="space-y-4">
              {features.map((feature, index) => (
                <Card key={index}>
                  <CardContent className="p-4 flex-row items-center space-x-3">
                    <View className="w-12 h-12 bg-primary/10 rounded-full items-center justify-center">
                      <feature.icon size={24} className="text-primary" />
                    </View>
                    <View className="flex-1">
                      <Text className="font-medium text-foreground">
                        {feature.title}
                      </Text>
                      <Text className="text-sm text-muted-foreground">
                        {feature.description}
                      </Text>
                    </View>
                  </CardContent>
                </Card>
              ))}
            </View>
          </View>
        );
  
      case 3:
        return (
          <View className="items-center space-y-6">
            <View className="w-24 h-24 bg-pink-100 rounded-full items-center justify-center">
              <CheckCircle size={48} className="text-primary" />
            </View>
            <View className="space-y-2 items-center">
              <Text className="text-2xl font-bold text-foreground">
                {t("onboarding.ready.title")}
              </Text>
              <Text className="text-xl text-primary">
                {t("onboarding.ready.subtitle")}
              </Text>
              <Text className="text-muted-foreground text-center max-w-xs">
                {t("onboarding.ready.description")}
              </Text>
            </View>
            <Badge
              variant="outline"
              className="bg-primary/10 text-primary border-primary/20"
            >
              {t(`modes.${selectedMode}`)}
            </Badge>
          </View>
        );
  
      default:
        return null;
    }
  };

  return (
    <View className="flex-1 bg-pink-50 justify-center items-center p-4">
      <Card className="w-full max-w-md shadow-xl">
        <CardHeader className="items-center relative">
          <Pressable
            onPress={toggleLanguage}
            className="absolute top-2 right-2 flex-row items-center p-2"
          >
            <Languages size={16} className="text-muted-foreground" />
            <Text className="ml-1 text-xs text-muted-foreground">{language?.toUpperCase() || "EN"}</Text>
          </Pressable>
          <View className="flex-row mb-4">
            {[0, 1, 2, 3].map((step) => (
              <View
                key={step}
                className={cn(
                  "w-2 h-2 mx-1 rounded-full",
                  step <= currentStep ? "bg-primary" : "bg-muted"
                )}
              />
            ))}
          </View>
        </CardHeader>

        <CardContent className="space-y-6">
          {renderStep()}

          <View className="flex-row justify-between mt-4">
            <Button
              variant="ghost"
              text={t("onboarding.buttons.skip")}
              onPress={() => onComplete(selectedMode || "planning")}
              className="text-muted-foreground"
            />
            <Button
              text={currentStep === 3
                ? t("onboarding.buttons.getStarted")
                : t("onboarding.buttons.next")}
              onPress={nextStep}
              disabled={currentStep === 1 && !selectedMode}
              className="bg-primary hover:bg-primary/90"
            />
          </View>
        </CardContent>
      </Card>
    </View>
  );
};

export default OnboardingFlow;
