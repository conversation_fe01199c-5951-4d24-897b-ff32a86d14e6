// components/OnboardingFlow.tsx
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { CheckCircle, ArrowRight, Languages, Heart, Brain, BarChart3 } from 'lucide-react-native';
import clsx from 'clsx';


type Mode = 'planning' | 'pregnancy' | 'newborn';

interface OnboardingFlowProps {
  onComplete: (selectedMode: Mode, date?: Date) => Promise<void>;
  language: string;
  toggleLanguage: () => void;
  t: (key: string) => string;
}

const OnboardingFlow: React.FC<OnboardingFlowProps> = ({ onComplete, language, toggleLanguage, t }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedMode, setSelectedMode] = useState<Mode | ''>('');

  const modes = [
    {
      id: 'planning' as Mode,
      icon: '🤰',
      title: t('onboarding.modes.planning.title'),
      description: t('onboarding.modes.planning.description')
    },
    {
      id: 'pregnancy' as Mode,
      icon: '🤱',
      title: t('onboarding.modes.pregnancy.title'),
      description: t('onboarding.modes.pregnancy.description')
    },
    {
      id: 'newborn' as Mode,
      icon: '👶',
      title: t('onboarding.modes.newborn.title'),
      description: t('onboarding.modes.newborn.description')
    }
  ];

  const features = [
    {
      icon: Brain,
      title: t('onboarding.features.ai.title'),
      description: t('onboarding.features.ai.description')
    },
    {
      icon: Heart,
      title: t('onboarding.features.tracking.title'),
      description: t('onboarding.features.tracking.description')
    },
    {
      icon: BarChart3,
      title: t('onboarding.features.insights.title'),
      description: t('onboarding.features.insights.description')
    }
  ];

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // selectedMode가 빈 문자열이면 기본값으로 'planning' 사용
      const modeToComplete = selectedMode || 'planning';
      onComplete(modeToComplete as Mode);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <View className="items-center">
            <Text className="text-display font-bold text-center text-foreground mb-6">{t('onboarding.welcome.title')}</Text>
            <Text className="text-heading text-primary text-center mb-4">{t('onboarding.welcome.subtitle')}</Text>
            <Text className="text-body text-muted-foreground text-center mb-section" style={{ maxWidth: 300 }}>{t('onboarding.welcome.description')}</Text>
            <View className="w-32 h-32 bg-primary rounded-full items-center justify-center shadow-card">
              <Text className="text-6xl">🤱</Text>
            </View>
          </View>
        );

      case 1:
        return (
          <View>
            <Text className="text-heading font-bold text-center text-foreground mb-tight">{t('onboarding.modes.title')}</Text>
            <Text className="text-body text-muted-foreground text-center mb-section">{t('onboarding.modes.subtitle')}</Text>
            <View>
              {modes.map((mode, index) => (
                <TouchableOpacity
                  key={mode.id}
                  onPress={() => setSelectedMode(mode.id)}
                  className={clsx(
                    'p-card rounded-lg border flex-row items-start shadow-card',
                    selectedMode === mode.id 
                      ? 'bg-primary border-primary' 
                      : 'bg-card border-border',
                    index < modes.length - 1 && 'mb-tight'
                  )}
                >
                  <Text className="text-2xl mr-3">{mode.icon}</Text>
                  <View className="flex-1">
                    <Text className={clsx(
                      "font-medium",
                      selectedMode === mode.id ? "text-primary-foreground" : "text-card-foreground"
                    )}>
                      {mode.title}
                    </Text>
                    <Text className={clsx(
                      "text-small",
                      selectedMode === mode.id ? "text-primary-foreground/80" : "text-muted-foreground"
                    )}>
                      {mode.description}
                    </Text>
                  </View>
                  {selectedMode === mode.id && <CheckCircle size={20} color="white" />}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 2:
        return (
          <View>
            <Text className="text-heading font-bold text-center text-foreground mb-tight">{t('onboarding.features.title')}</Text>
            <Text className="text-body text-muted-foreground text-center mb-section">{t('onboarding.features.subtitle')}</Text>
            <View>
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <View key={index} className={clsx(
                    "flex-row items-center bg-card p-card rounded-lg border border-border shadow-card",
                    index < features.length - 1 && "mb-tight"
                  )}>
                    <View className="w-12 h-12 bg-primary/10 rounded-full items-center justify-center mr-3">
                      <Icon size={24} color="#e96984" />
                    </View>
                    <View className="flex-1">
                      <Text className="font-medium text-card-foreground">{feature.title}</Text>
                      <Text className="text-small text-muted-foreground">{feature.description}</Text>
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        );

      case 3:
        return (
          <View className="items-center">
            <View className="w-24 h-24 bg-primary rounded-full items-center justify-center mb-section shadow-card">
              <CheckCircle size={48} color="white" />
            </View>
            <Text className="text-heading font-bold text-foreground mb-tight">{t('onboarding.ready.title')}</Text>
            <Text className="text-body text-primary mb-tight">{t('onboarding.ready.subtitle')}</Text>
            <Text className="text-body text-muted-foreground text-center mb-section" style={{ maxWidth: 300 }}>{t('onboarding.ready.description')}</Text>
            <View className="px-3 py-1 rounded-full border border-primary/20 bg-primary/10">
              <Text className="text-small text-primary">{t(`modes.${selectedMode}`)}</Text>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1 }} className="bg-secondary flex-1">
      <View className="flex-1 items-center justify-center p-card">
        <View className="w-full" style={{ maxWidth: 400 }}>
          <View className="relative items-end">
            <TouchableOpacity 
              onPress={toggleLanguage} 
              className="absolute top-0 right-0 flex-row items-center p-tight rounded-full bg-secondary-foreground/5"
            >
              <Languages size={16} className="text-secondary-foreground" />
              <Text className="text-label ml-1 text-secondary-foreground">{language.toUpperCase()}</Text>
            </TouchableOpacity>
            <View className="flex-row justify-center mb-section w-full">
              {[0, 1, 2, 3].map((step) => (
                <View
                  key={step}
                  className={clsx(
                    'w-2 h-2 rounded-full mx-1', 
                    step <= currentStep ? 'bg-primary' : 'bg-muted'
                  )}
                />
              ))}
            </View>
          </View>

          {renderStep()}

          <View className="flex-row justify-between items-center mt-section">
            <TouchableOpacity 
              onPress={() => onComplete((selectedMode || 'planning') as Mode)}
              className="p-tight"
            >
              <Text className="text-small text-muted-foreground">{t('onboarding.buttons.skip')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={nextStep}
              disabled={currentStep === 1 && !selectedMode}
              className={clsx(
                "bg-primary px-4 py-2 rounded-lg flex-row items-center shadow-card",
                (currentStep === 1 && !selectedMode) && "opacity-50"
              )}
            >
              <Text className="text-body font-medium text-primary-foreground mr-2">
                {currentStep === 3 ? t('onboarding.buttons.getStarted') : t('onboarding.buttons.next')}
              </Text>
              <ArrowRight size={16} color="white" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default OnboardingFlow;