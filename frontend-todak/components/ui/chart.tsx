import { cn } from "@/lib/utils";
import * as React from "react";
import { Text, View } from "react-native";

const THEMES = { light: "", dark: ".dark" } as const;

export type ChartConfig = {
  [k in string]: {
    label?: React.ReactNode;
    icon?: React.ComponentType;
  } & (
    | { color?: string; theme?: never }
    | { color?: never; theme: Record<keyof typeof THEMES, string> }
  );
};

type ChartContextProps = {
  config: ChartConfig;
};

const ChartContext = React.createContext<ChartContextProps | null>(null);

function useChart() {
  const context = React.useContext(ChartContext);
  if (!context) throw new Error("useChart must be used within a <ChartContainer />");
  return context;
}

interface ChartContainerProps {
  id?: string;
  className?: string;
  children: React.ReactNode;
  config: ChartConfig;
}

const ChartContainer = React.forwardRef<View, ChartContainerProps>(
  ({ className, children, config, ...props }, ref) => {
    return (
      <ChartContext.Provider value={{ config }}>
        <View
          ref={ref}
          className={cn("flex-1 justify-center items-center", className)}
          {...props}
        >
          {children}
        </View>
      </ChartContext.Provider>
    );
  }
);
ChartContainer.displayName = "Chart";

// ChartStyle is not needed in React Native

// Simple tooltip component for React Native
interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

interface ChartTooltipContentProps {
  active?: boolean;
  payload?: any[];
  className?: string;
  indicator?: "dot" | "line" | "dashed";
  hideLabel?: boolean;
  hideIndicator?: boolean;
  label?: string;
  labelFormatter?: (value: any, payload: any[]) => React.ReactNode;
  labelClassName?: string;
  formatter?: any;
  color?: string;
  nameKey?: string;
}

const ChartTooltipContent = React.forwardRef<View, ChartTooltipContentProps>(
  (
    {
      active,
      payload,
      className,
      indicator = "dot",
      hideLabel = false,
      hideIndicator = false,
      label,
      labelFormatter,
      labelClassName,
      color,
      nameKey,
    },
    ref
  ) => {
    const { config } = useChart();

    if (!active || !payload?.length) return null;

    return (
      <View
        ref={ref}
        className={cn(
          "bg-white p-3 rounded-lg border border-gray-200 shadow-lg",
          className
        )}
      >
        {!hideLabel && label && (
          <Text className={cn("font-medium text-gray-900 mb-2", labelClassName)}>
            {labelFormatter ? labelFormatter(label, payload) : label}
          </Text>
        )}

        {payload.map((item: any, index: number) => {
          const key = `${nameKey || item.name || item.dataKey || "value"}`;
          const itemConfig = config[key];
          const indicatorColor = color || item.color || "#6366F1";

          return (
            <View key={index} className="flex-row items-center mb-1">
              {!hideIndicator && (
                <View
                  className={cn("rounded mr-2", {
                    "w-2 h-2": indicator === "dot",
                    "w-1 h-4": indicator === "line",
                  })}
                  style={{ backgroundColor: indicatorColor }}
                />
              )}
              <Text className="text-gray-700 flex-1">
                {itemConfig?.label || item.name}: {item.value}
              </Text>
            </View>
          );
        })}
      </View>
    );
  }
);
ChartTooltipContent.displayName = "ChartTooltip";

// Simple legend component for React Native
interface ChartLegendProps {
  payload?: any[];
  className?: string;
  hideIcon?: boolean;
  verticalAlign?: "top" | "bottom";
  nameKey?: string;
}

const ChartLegend: React.FC<ChartLegendProps> = () => {
  return null; // Placeholder for legend functionality
};

// Simple tooltip component for React Native
const ChartTooltip: React.FC<ChartTooltipProps> = () => {
  return null; // Placeholder for tooltip functionality
};

interface ChartLegendContentProps {
  className?: string;
  hideIcon?: boolean;
  payload?: any[];
  verticalAlign?: "top" | "bottom";
  nameKey?: string;
}

const ChartLegendContent = React.forwardRef<View, ChartLegendContentProps>(
  ({ className, hideIcon = false, payload, verticalAlign = "bottom", nameKey }, ref) => {
    const { config } = useChart();
    if (!payload?.length) return null;

    return (
      <View
        ref={ref}
        className={cn(
          "flex-row items-center justify-center gap-4",
          verticalAlign === "top" ? "pb-3" : "pt-3",
          className
        )}
      >
        {payload.map((item: any, index: number) => {
          const key = `${nameKey || item.dataKey || "value"}`;
          const itemConfig = config[key];
          return (
            <View key={index} className="flex-row items-center gap-1.5">
              {!hideIcon && (
                <View
                  className="h-2 w-2 rounded"
                  style={{ backgroundColor: item.color || "#6366F1" }}
                />
              )}
              <Text className="text-sm text-gray-700">
                {itemConfig?.label || item.value}
              </Text>
            </View>
          );
        })}
      </View>
    );
  }
);
ChartLegendContent.displayName = "ChartLegend";

export {
    ChartContainer, ChartLegend,
    ChartLegendContent, ChartTooltip,
    ChartTooltipContent
};

