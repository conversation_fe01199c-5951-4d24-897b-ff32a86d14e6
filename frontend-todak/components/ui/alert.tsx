// components/ui/alert.ts

import { cn } from "@/lib/utils"; // 또는 custom cn()
import * as React from "react";
import { Text, TextProps, View, ViewProps } from "react-native";
import { tv, type VariantProps } from "tailwind-variants";

const alertVariants = tv({
  base: "relative w-full rounded-lg border p-4",
  variants: {
    variant: {
      default: "bg-background text-foreground border-border",
      destructive: "bg-background border-destructive/50 text-destructive",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

export interface AlertProps
  extends ViewProps,
    VariantProps<typeof alertVariants> {}

export const Alert = React.forwardRef<View, AlertProps>(
  ({ variant, className, ...props }, ref) => {
    return (
      <View
        ref={ref}
        className={cn(alertVariants({ variant }), className)}
        {...props}
      />
    );
  }
);
Alert.displayName = "Alert";

export const AlertTitle = React.forwardRef<Text, TextProps>(
  ({ className, ...props }, ref) => {
    return (
      <Text
        ref={ref}
        className={cn("mb-1 font-medium text-base", className)}
        {...props}
      />
    );
  }
);
AlertTitle.displayName = "AlertTitle";

export const AlertDescription = React.forwardRef<Text, TextProps>(
  ({ className, ...props }, ref) => {
    return (
      <Text
        ref={ref}
        className={cn("text-sm text-muted-foreground", className)}
        {...props}
      />
    );
  }
);
AlertDescription.displayName = "AlertDescription";
