// components/ui/Checkbox.tsx

import { Check } from "lucide-react-native"
import React from "react"
import {
    GestureResponderEvent,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native"
import Animated, {
    FadeIn,
    FadeOut,
} from "react-native-reanimated"

type CheckboxProps = {
  label?: string
  checked: boolean
  onChange: (checked: boolean) => void
  disabled?: boolean
  size?: number
  style?: object
}

export const AnimatedCheckbox: React.FC<CheckboxProps> = ({
  label,
  checked,
  onChange,
  disabled = false,
  size = 20,
  style = {},
}) => {
  const handlePress = (e: GestureResponderEvent) => {
    if (!disabled) onChange(!checked)
  }

  return (
    <TouchableOpacity
      onPress={handlePress}
      activeOpacity={0.7}
      disabled={disabled}
      style={[styles.container, style, disabled && styles.disabled]}
    >
      <View
        style={[
          styles.box,
          {
            width: size,
            height: size,
            borderColor: checked ? "#3b82f6" : "#ccc",
            backgroundColor: checked ? "#3b82f6" : "#fff",
          },
        ]}
      >
        {checked && (
          <Animated.View entering={FadeIn} exiting={FadeOut}>
            <Check color="white" size={size * 0.8} />
          </Animated.View>
        )}
      </View>
      {label && (
        <Text
          style={[
            styles.label,
            disabled && { color: "#aaa" },
          ]}
        >
          {label}
        </Text>
      )}
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  box: {
    borderWidth: 2,
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  label: {
    marginLeft: 8,
    fontSize: 16,
    color: "#333",
  },
  disabled: {
    opacity: 0.5,
  },
})
