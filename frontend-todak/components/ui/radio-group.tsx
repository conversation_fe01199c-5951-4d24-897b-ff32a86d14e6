import React, { useEffect, useRef } from "react"
import { View, Text, Pressable, Animated } from "react-native"
import { Circle } from "lucide-react-native"
import { cn } from "@/lib/utils"

export type RadioGroupItemProps = {
  value: string
  label: string
  icon?: React.ReactNode
  iconPosition?: "left" | "right"
  disabled?: boolean
  isSelected?: boolean
  onSelect?: () => void
  className?: string
}

export const RadioGroupItem = ({
  value,
  label,
  icon,
  iconPosition = "left",
  disabled,
  isSelected,
  onSelect,
  className,
}: RadioGroupItemProps) => {
  const scale = useRef(new Animated.Value(1)).current

  useEffect(() => {
    Animated.spring(scale, {
      toValue: isSelected ? 1.2 : 1,
      useNativeDriver: true,
    }).start()
  }, [isSelected])

  const content = (
    <Animated.View
      className={cn(
        "flex-row items-center rounded-md px-3 py-2 border border-primary",
        disabled && "opacity-40",
        isSelected && "bg-primary/10",
        className
      )}
      style={{ transform: [{ scale }] }}
    >
      {iconPosition === "left" && (
        <View className="mr-2">
          {icon ?? <Circle size={16} color={isSelected ? "#0f0" : "#999"} />}
        </View>
      )}
      <Text className="text-sm">{label}</Text>
      {iconPosition === "right" && (
        <View className="ml-2">
          {icon ?? <Circle size={16} color={isSelected ? "#0f0" : "#999"} />}
        </View>
      )}
    </Animated.View>
  )

  return (
    <Pressable
      accessibilityRole="radio"
      accessibilityState={{ selected: !!isSelected, disabled }}
      disabled={disabled}
      onPress={onSelect}
    >
      {content}
    </Pressable>
  )
}

type Direction = "horizontal" | "vertical"

type SingleSelectProps = {
  multi?: false
  value: string
  onValueChange: (val: string) => void
}

type MultiSelectProps = {
  multi: true
  value: string[]
  onValueChange: (val: string[]) => void
}

type RadioGroupProps = (SingleSelectProps | MultiSelectProps) & {
  children: React.ReactNode
  direction?: Direction
  className?: string
}

export const RadioGroup = ({
  value,
  onValueChange,
  multi = false,
  children,
  direction = "vertical",
  className,
}: RadioGroupProps) => {
  return (
    <View className={cn(direction === "horizontal" ? "flex-row gap-4" : "flex-col gap-2", className)}>
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          return React.cloneElement(
            child as React.ReactElement<RadioGroupItemProps>,
            {
              isSelected: multi
                ? (value as string[]).includes(child.props.value)
                : value === child.props.value,
              onSelect: () => {
                if (multi) {
                  const current = value as string[]
                  const next = current.includes(child.props.value)
                    ? current.filter(v => v !== child.props.value)
                    : [...current, child.props.value]
                  onValueChange(next)
                } else {
                  onValueChange(child.props.value)
                }
              },
            }
          )
        }
        return child
      })}
    </View>
  )
}