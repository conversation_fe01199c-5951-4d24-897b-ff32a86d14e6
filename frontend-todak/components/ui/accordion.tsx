// components/ui/accordion.tsx
import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react-native";
import React, { useState } from "react";
import { LayoutAnimation, Platform, Pressable, Text, UIManager, View } from "react-native";

// 안드로이드에서 LayoutAnimation 활성화
if (Platform.OS === "android" && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface AccordionRootProps {
  children: React.ReactNode;
  className?: string;
}

interface AccordionItemProps {
  id: string;
  title: React.ReactNode;
  children: React.ReactNode;
  openId: string | null;
  onToggle: (id: string) => void;
  className?: string;
}

// ✅ Root
export const Accordion: React.FC<AccordionRootProps> = ({ children, className }) => {
  const [openId, setOpenId] = useState<string | null>(null);

  const handleToggle = (id: string) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setOpenId(prev => (prev === id ? null : id));
  };

  // children은 AccordionItem들
  const enhancedChildren = React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) return child;
  
    return React.cloneElement(child as React.ReactElement<any>, {
      openId,
      onToggle: handleToggle,
    });
  });

  return <View className={className}>{enhancedChildren}</View>;
};

// ✅ Item
export const AccordionItem: React.FC<AccordionItemProps> = ({
  id,
  title,
  children,
  openId,
  onToggle,
  className,
}) => {
  const isOpen = id === openId;

  return (
    <View className={cn("border-b border-border", className)}>
      <Pressable
        onPress={() => onToggle(id)}
        className="flex-row justify-between items-center py-4"
      >
        <Text className="text-base font-medium">{title}</Text>
        <ChevronDown
          size={16}
          className={cn(
            "text-muted-foreground transition-transform",
            isOpen ? "rotate-180" : "rotate-0"
          )}
        />
      </Pressable>

      {isOpen && (
        <View className="pt-0 pb-4">
          <Text className="text-sm text-muted-foreground">{children}</Text>
        </View>
      )}
    </View>
  );
};
