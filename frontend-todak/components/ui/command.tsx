// React Native Command Palette Components
import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from "react-native";
import Icon from // React Native Command Palette Components
import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from "react-native";
import Icon from "react-native-vector-icons/Feather";

export const CommandDialog = ({ visible, onClose, children }) => (
  <Modal visible={visible} onRequestClose={onClose} transparent animationType="fade">
    <View style={styles.overlay}>
      <View style={styles.dialog}>{children}</View>
    </View>
  </Modal>
);

export const CommandInput = ({ value, onChange, placeholder = "Search..." }) => (
  <View style={styles.inputWrapper}>
    <Icon name="search" size={18} color="#999" style={{ marginRight: 8 }} />
    <TextInput
      placeholder={placeholder}
      style={styles.input}
      value={value}
      onChangeText={onChange}
      autoFocus
    />
  </View>
);

export const CommandList = ({ items, onSelect, emptyLabel = "No results" }) => (
  items.length === 0 ? (
    <CommandEmpty label={emptyLabel} />
  ) : (
    <FlatList
      data={items}
      keyExtractor={(item) => item.label}
      renderItem={({ item }) => (
        <CommandItem label={item.label} onPress={item.action} />
      )}
    />
  )
);

export const CommandEmpty = ({ label }) => (
  <Text style={styles.empty}>{label}</Text>
);

export const CommandGroup = ({ title, children }) => (
  <View style={styles.group}>
    {title && <Text style={styles.groupTitle}>{title}</Text>}
    <View>{children}</View>
  </View>
);

export const CommandItem = ({ label, onPress, disabled = false }) => (
  <TouchableOpacity
    onPress={!disabled ? onPress : undefined}
    style={[styles.item, disabled && styles.itemDisabled]}
    disabled={disabled}
  >
    <Text style={styles.itemText}>{label}</Text>
  </TouchableOpacity>
);

export const CommandSeparator = () => <View style={styles.separator} />;

export const CommandShortcut = ({ label }) => (
  <Text style={styles.shortcut}>{label}</Text>
);

export const Command = ({ children }) => (
  <View style={{ width: "100%" }}>{children}</View>
);

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.4)",
    justifyContent: "center",
    padding: 20,
  },
  dialog: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 12,
    elevation: 4,
    maxHeight: "80%",
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderBottomColor: "#ccc",
    borderBottomWidth: 1,
    paddingBottom: 6,
    marginBottom: 10,
  },
  input: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  item: {
    paddingVertical: 10,
  },
  itemText: {
    fontSize: 16,
    color: "#333",
  },
  itemDisabled: {
    opacity: 0.5,
  },
  empty: {
    paddingVertical: 20,
    textAlign: "center",
    color: "#888",
  },
  group: {
    paddingVertical: 8,
  },
  groupTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#666",
    marginBottom: 4,
  },
  separator: {
    height: 1,
    backgroundColor: "#ccc",
    marginVertical: 6,
  },
  shortcut: {
    fontSize: 12,
    color: "#999",
    textAlign: "right",
  },
});
";

export const CommandDialog = ({ visible, onClose, children }) => (
  <Modal visible={visible} onRequestClose={onClose} transparent animationType="fade">
    <View style={styles.overlay}>
      <View style={styles.dialog}>{children}</View>
    </View>
  </Modal>
);

export const CommandInput = ({ value, onChange, placeholder = "Search..." }) => (
  <View style={styles.inputWrapper}>
    <Icon name="search" size={18} color="#999" style={{ marginRight: 8 }} />
    <TextInput
      placeholder={placeholder}
      style={styles.input}
      value={value}
      onChangeText={onChange}
      autoFocus
    />
  </View>
);

export const CommandList = ({ items, onSelect, emptyLabel = "No results" }) => (
  items.length === 0 ? (
    <CommandEmpty label={emptyLabel} />
  ) : (
    <FlatList
      data={items}
      keyExtractor={(item) => item.label}
      renderItem={({ item }) => (
        <CommandItem label={item.label} onPress={item.action} />
      )}
    />
  )
);

export const CommandEmpty = ({ label }) => (
  <Text style={styles.empty}>{label}</Text>
);

export const CommandGroup = ({ title, children }) => (
  <View style={styles.group}>
    {title && <Text style={styles.groupTitle}>{title}</Text>}
    <View>{children}</View>
  </View>
);

export const CommandItem = ({ label, onPress, disabled = false }) => (
  <TouchableOpacity
    onPress={!disabled ? onPress : undefined}
    style={[styles.item, disabled && styles.itemDisabled]}
    disabled={disabled}
  >
    <Text style={styles.itemText}>{label}</Text>
  </TouchableOpacity>
);

export const CommandSeparator = () => <View style={styles.separator} />;

export const CommandShortcut = ({ label }) => (
  <Text style={styles.shortcut}>{label}</Text>
);

export const Command = ({ children }) => (
  <View style={{ width: "100%" }}>{children}</View>
);

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.4)",
    justifyContent: "center",
    padding: 20,
  },
  dialog: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 12,
    elevation: 4,
    maxHeight: "80%",
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderBottomColor: "#ccc",
    borderBottomWidth: 1,
    paddingBottom: 6,
    marginBottom: 10,
  },
  input: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  item: {
    paddingVertical: 10,
  },
  itemText: {
    fontSize: 16,
    color: "#333",
  },
  itemDisabled: {
    opacity: 0.5,
  },
  empty: {
    paddingVertical: 20,
    textAlign: "center",
    color: "#888",
  },
  group: {
    paddingVertical: 8,
  },
  groupTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#666",
    marginBottom: 4,
  },
  separator: {
    height: 1,
    backgroundColor: "#ccc",
    marginVertical: 6,
  },
  shortcut: {
    fontSize: 12,
    color: "#999",
    textAlign: "right",
  },
});
