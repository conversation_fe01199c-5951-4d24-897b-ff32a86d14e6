// components/ui/AlertDialog.tsx
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react-native";
import React from "react";
import {
    KeyboardAvoidingView,
    Modal,
    Platform,
    Text,
    View
} from "react-native";

interface AlertDialogProps {
  isVisible: boolean;
  onCancel?: () => void;
  onConfirm: () => void;
  title: string;
  description?: string;
  icon?: React.ReactElement<LucideIcon>;
  cancelText?: string;
  confirmText?: string;
  singleAction?: boolean;
  confirmVariant?: "default" | "destructive" | "outline";
}

export const AlertDialog: React.FC<AlertDialogProps> = ({
  isVisible,
  onCancel,
  onConfirm,
  title,
  description,
  icon,
  cancelText = "취소",
  confirmText = "확인",
  singleAction = false,
  confirmVariant = "default",
}) => {
  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        className="flex-1 justify-center items-center bg-black/60 px-6"
      >
        <View className="w-full max-w-md bg-background p-6 rounded-lg space-y-4">
          {icon && <View className="items-center">{icon}</View>}
          <Text className="text-lg font-semibold text-foreground text-center">
            {title}
          </Text>
          {description && (
            <Text className="text-sm text-muted-foreground text-center">
              {description}
            </Text>
          )}

          <View
            className={cn(
              "mt-4",
              singleAction ? "items-center" : "flex-row justify-end space-x-2"
            )}
          >
            {!singleAction && onCancel && (
              <Button
                variant="outline"
                onPress={onCancel}
                className="px-4 py-2"
              >
                {cancelText}
              </Button>
            )}
            <Button
              variant={confirmVariant}
              onPress={onConfirm}
              className="px-4 py-2"
            >
              {confirmText}
            </Button>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};
