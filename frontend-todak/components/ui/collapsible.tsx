import React, { useEffect, useRef, useState } from "react";
import {
    Animated,
    LayoutAnimation,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    UIManager,
    View,
} from "react-native";

if (Platform.OS === "android" && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const Collapsible = ({ title, children, defaultOpen = false }: {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}) => {
  const [open, setOpen] = useState(defaultOpen);
  const animatedController = useRef(new Animated.Value(defaultOpen ? 1 : 0)).current;

  useEffect(() => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    Animated.timing(animatedController, {
      duration: 300,
      toValue: open ? 1 : 0,
      useNativeDriver: false,
    }).start();
  }, [open]);

  const height = animatedController.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1000], // 충분히 큰 값으로 설정
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={() => setOpen(!open)}
        activeOpacity={0.8}
      >
        <Text style={styles.headerText}>{title}</Text>
      </TouchableOpacity>

      <Animated.View style={{ height, overflow: "hidden" }}>
        <View style={styles.content}>
          {children}
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
    borderRadius: 8,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#ddd",
  },
  header: {
    padding: 14,
    backgroundColor: "#f0f0f0",
  },
  headerText: {
    fontSize: 16,
    fontWeight: "600",
  },
  content: {
    padding: 14,
    backgroundColor: "#fff",
  },
});

export default Collapsible;
