// components/ui/Calendar.tsx

import React, { useState } from "react";
import { View, Text } from "react-native";
import DatePicker from "react-native-ui-datepicker";
import dayjs from "dayjs";
import { ChevronLeft, ChevronRight } from "lucide-react-native";
import { cn } from "@/lib/utils"; // Tailwind merge helper
import { buttonVariants } from "@/components/ui/button"; // Variant-styled buttons

export interface CalendarProps {
  date?: Date;
  onChange?: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
}

export function Calendar({
  date,
  onChange,
  minDate,
  maxDate,
  className,
}: CalendarProps) {
  const [selectedDate, setSelectedDate] = useState(date || new Date());

  const handleDateChange = (newDate: Date) => {
    setSelectedDate(newDate);
    onChange?.(newDate);
  };

  return (
    <View className={cn("p-4", className)}>
      <DatePicker
        mode="calendar"
        selected={selectedDate}
        onDateChange={handleDateChange}
        minDate={minDate}
        maxDate={maxDate}
        options={{
          defaultFont: "System",
          mainColor: "#6366F1", // Tailwind primary
          borderColor: "#E5E7EB",
          textHeaderColor: "#111827",
          textDefaultColor: "#1F2937",
        }}
        renderArrowLeft={() => <ChevronLeft size={20} color="#6B7280" />}
        renderArrowRight={() => <ChevronRight size={20} color="#6B7280" />}
      />
    </View>
  );
}
