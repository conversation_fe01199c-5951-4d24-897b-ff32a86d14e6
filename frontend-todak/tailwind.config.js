/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all files that contain Nativewind classes.
  content: ["./App.tsx", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        background: 'hsl(355, 25%, 98%)',
        foreground: 'hsl(340, 15%, 15%)',

        card: 'hsl(355, 20%, 97%)',
        'card-foreground': 'hsl(340, 15%, 15%)',

        popover: 'hsl(355, 20%, 97%)',
        'popover-foreground': 'hsl(340, 15%, 15%)',

        primary: 'hsl(342, 65%, 70%)',
        'primary-foreground': 'hsl(0, 0%, 100%)',

        secondary: 'hsl(350, 25%, 92%)',
        'secondary-foreground': 'hsl(340, 35%, 25%)',

        muted: 'hsl(355, 20%, 94%)',
        'muted-foreground': 'hsl(340, 10%, 50%)',

        accent: 'hsl(348, 20%, 90%)',
        'accent-foreground': 'hsl(340, 35%, 20%)',

        destructive: 'hsl(0, 84.2%, 60.2%)',
        'destructive-foreground': 'hsl(0, 0%, 98%)',

        border: 'hsl(350, 15%, 88%)',
        input: 'hsl(350, 15%, 88%)',
        ring: 'hsl(342, 65%, 70%)',
      },
      borderRadius: {
        lg: '0.75rem', // ≈ 12px
        xl: '1rem',
        '2xl': '1.25rem',
      },
    },
  },
  plugins: [],
};